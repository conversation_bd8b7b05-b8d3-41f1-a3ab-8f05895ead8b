import { useState, useEffect } from 'react';
import { HijriDate } from '../types';
import api from '../services/api';

interface UseHijriDateResult {
  hijriDate: HijriDate | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useHijriDate = (): UseHijriDateResult => {
  const [hijriDate, setHijriDate] = useState<HijriDate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHijriDate = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.hijriDate.getToday();
      
      if (response.code === 200 && response.data && response.data.hijri) {
        const hijri = response.data.hijri;
        const date: HijriDate = {
          date: `${hijri.day} ${hijri.month.en} ${hijri.year}`,
          day: parseInt(hijri.day),
          month: {
            number: hijri.month.number,
            en: hijri.month.en,
            ar: hijri.month.ar,
          },
          year: parseInt(hijri.year),
          designation: hijri.designation,
        };
        setHijriDate(date);
      } else {
        throw new Error('Invalid Hijri date data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch Hijri date');
      console.error('Error fetching Hijri date:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await fetchHijriDate();
  };

  useEffect(() => {
    fetchHijriDate();
  }, []);

  return {
    hijriDate,
    loading,
    error,
    refetch,
  };
};
