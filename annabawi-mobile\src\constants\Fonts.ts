// Font constants sesuai dengan frontend design system
export const Fonts = {
  // Font Families - sesuai dengan frontend
  primary: 'Inter', // Default font
  secondary: 'Montser<PERSON>', // Alternative font
  tertiary: 'Roboto', // System font fallback
  arabic: '<PERSON><PERSON>', // Arabic font untuk teks Arab
  
  // Font Sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
  },
  
  // Font Weights
  weights: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  // Line Heights
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Font styles untuk komponen
export const FontStyles = {
  // Headers
  h1: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes['4xl'],
    fontWeight: Fonts.weights.bold,
    lineHeight: Fonts.lineHeights.tight,
  },
  h2: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes['3xl'],
    fontWeight: Fonts.weights.bold,
    lineHeight: Fonts.lineHeights.tight,
  },
  h3: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes['2xl'],
    fontWeight: Fonts.weights.semibold,
    lineHeight: Fonts.lineHeights.normal,
  },
  h4: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.xl,
    fontWeight: Fonts.weights.semibold,
    lineHeight: Fonts.lineHeights.normal,
  },
  
  // Body text
  body: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.base,
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.normal,
  },
  bodyLarge: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.lg,
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.normal,
  },
  bodySmall: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.sm,
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.normal,
  },
  
  // Special text
  caption: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.xs,
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.normal,
  },
  button: {
    fontFamily: Fonts.primary,
    fontSize: Fonts.sizes.base,
    fontWeight: Fonts.weights.semibold,
    lineHeight: Fonts.lineHeights.tight,
  },
  
  // Arabic text
  arabic: {
    fontFamily: Fonts.arabic,
    fontSize: Fonts.sizes.lg,
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.relaxed,
  },
  arabicLarge: {
    fontFamily: Fonts.arabic,
    fontSize: Fonts.sizes['2xl'],
    fontWeight: Fonts.weights.normal,
    lineHeight: Fonts.lineHeights.relaxed,
  },
};
