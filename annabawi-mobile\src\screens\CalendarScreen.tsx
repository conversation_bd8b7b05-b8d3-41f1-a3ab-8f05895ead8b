import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';
import { Event } from '../types';

const CalendarScreen: React.FC = () => {
  const { theme } = useTheme();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadEvents();
  }, []);

  const loadEvents = async () => {
    try {
      // Mock data - replace with actual API call
      const mockEvents: Event[] = [
        {
          id: 1,
          title: '<PERSON><PERSON><PERSON>',
          speaker: '<PERSON><PERSON><PERSON>',
          start_time: '2025-01-20 19:30:00',
          end_time: '2025-01-20 21:00:00',
          location: 'Masjid An-Nabawi',
          description: 'Kajian rutin tafsir Al-Quran setiap <PERSON>gu malam',
          tags: ['kajian', 'tafsir'],
        },
        {
          id: 2,
          title: '<PERSON>gajian <PERSON>',
          speaker: 'Ustadz <PERSON>',
          start_time: '2025-01-25 20:00:00',
          end_time: '2025-01-25 22:00:00',
          location: 'Aula Masjid',
          description: 'Pengajian akbar bulanan dengan tema akhlak mulia',
          tags: ['pengajian', 'akhlak'],
        },
      ];
      setEvents(mockEvents);
    } catch (error) {
      console.error('Error loading events:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEvents();
    setRefreshing(false);
  };

  const formatEventDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatEventTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderEventCard = (event: Event) => (
    <TouchableOpacity
      key={event.id}
      style={[styles.eventCard, { backgroundColor: theme.cardBackground }]}
    >
      <View style={styles.eventHeader}>
        <Text style={[styles.eventTitle, { color: theme.text }]} numberOfLines={2}>
          {event.title}
        </Text>
        <Text style={[styles.eventSpeaker, { color: theme.islamicGreen }]}>
          {event.speaker}
        </Text>
      </View>
      
      <View style={styles.eventDetails}>
        <View style={styles.eventDetailRow}>
          <Ionicons name="calendar" size={16} color={theme.textSecondary} />
          <Text style={[styles.eventDetailText, { color: theme.textSecondary }]}>
            {formatEventDate(event.start_time)}
          </Text>
        </View>
        
        <View style={styles.eventDetailRow}>
          <Ionicons name="time" size={16} color={theme.textSecondary} />
          <Text style={[styles.eventDetailText, { color: theme.textSecondary }]}>
            {formatEventTime(event.start_time)} - {formatEventTime(event.end_time)} WIB
          </Text>
        </View>
        
        <View style={styles.eventDetailRow}>
          <Ionicons name="location" size={16} color={theme.textSecondary} />
          <Text style={[styles.eventDetailText, { color: theme.textSecondary }]}>
            {event.location}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.eventDescription, { color: theme.text }]} numberOfLines={2}>
        {event.description}
      </Text>
      
      <View style={styles.tagsContainer}>
        {event.tags.slice(0, 2).map((tag, index) => (
          <View key={index} style={[styles.tag, { backgroundColor: theme.islamicGreen + '20' }]}>
            <Text style={[styles.tagText, { color: theme.islamicGreen }]}>
              {tag}
            </Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Kalender Kajian</Text>
      </View>
      
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: theme.textSecondary }]}>
              Memuat jadwal kajian...
            </Text>
          </View>
        ) : (
          <View style={styles.eventsContainer}>
            {events.map(renderEventCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    ...FontStyles.h2,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Layout.spacing['2xl'],
  },
  loadingText: {
    ...FontStyles.body,
  },
  eventsContainer: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
  },
  eventCard: {
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  eventHeader: {
    marginBottom: Layout.spacing.sm,
  },
  eventTitle: {
    ...FontStyles.h4,
    marginBottom: Layout.spacing.xs,
    lineHeight: 22,
  },
  eventSpeaker: {
    ...FontStyles.bodySmall,
    fontWeight: '600',
  },
  eventDetails: {
    marginBottom: Layout.spacing.sm,
  },
  eventDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.xs,
  },
  eventDetailText: {
    ...FontStyles.bodySmall,
    marginLeft: Layout.spacing.sm,
  },
  eventDescription: {
    ...FontStyles.body,
    lineHeight: 20,
    marginBottom: Layout.spacing.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: Layout.spacing.xs,
  },
  tag: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
  },
  tagText: {
    ...FontStyles.caption,
    fontSize: 10,
  },
});

export default CalendarScreen;
