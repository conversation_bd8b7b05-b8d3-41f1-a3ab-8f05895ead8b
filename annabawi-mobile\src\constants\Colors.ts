// Color constants berdasarkan design system frontend
export const Colors = {
  // Islamic Colors - sesuai dengan frontend
  islamicGreen: '#008037', // hsl(140, 100%, 15%)
  islamicGreenLight: '#0A5C36', // hsl(140, 50%, 25%)
  islamicGold: '#FFD700', // hsl(51, 100%, 50%)
  islamicGoldLight: '#FFF8DC', // hsl(51, 100%, 85%)

  // Base Colors
  white: '#FFFFFF',
  black: '#000000',
  
  // Background Colors
  background: '#FFFFFF',
  backgroundSecondary: '#F5F5F5',
  backgroundDark: '#1A1A1A',
  
  // Text Colors
  text: '#2D5016', // hsl(140, 65%, 15%)
  textSecondary: '#737373',
  textLight: '#A3A3A3',
  textDark: '#FFFFFF',
  
  // Border Colors
  border: '#E5E5E5',
  borderLight: '#F0F0F0',
  
  // Status Colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Gradient Colors
  gradientStart: '#008037',
  gradientEnd: '#0A5C36',
  
  // Shadow Colors
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
  
  // Prayer Time Colors
  prayerActive: '#FFD700',
  prayerInactive: '#E5E5E5',
  prayerNext: '#FFA500',
  
  // Tab Bar Colors
  tabBarActive: '#008037',
  tabBarInactive: '#737373',
  tabBarBackground: '#FFFFFF',
  
  // Card Colors
  cardBackground: '#FFFFFF',
  cardShadow: 'rgba(0, 128, 55, 0.1)',
  
  // Shortcut Colors
  shortcutQuran: '#4CAF50',
  shortcutWirid: '#FF9800',
  shortcutSholat: '#2196F3',
  shortcutQiblat: '#9C27B0',
  shortcutTahlil: '#607D8B',
  shortcutMaulid: '#FF5722',
  shortcutZakat: '#E91E63',
  shortcutOther: '#795548',
};

// Dark theme colors
export const DarkColors = {
  ...Colors,
  background: '#1A1A1A',
  backgroundSecondary: '#2D2D2D',
  text: '#FFFFFF',
  textSecondary: '#B3B3B3',
  cardBackground: '#2D2D2D',
  tabBarBackground: '#1A1A1A',
  border: '#404040',
  borderLight: '#333333',
};

// Theme type
export type Theme = typeof Colors;
