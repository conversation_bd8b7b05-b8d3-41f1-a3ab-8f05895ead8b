import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

const SettingsScreen: React.FC = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();

  const handleLogin = () => {
    // Navigate to login screen
    console.log('Navigate to login');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Apakah Anda yakin ingin keluar?',
      [
        { text: 'Batal', style: 'cancel' },
        { 
          text: 'Keluar', 
          style: 'destructive',
          onPress: logout 
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'Tentang Aplikasi',
      'An-Nabawi Mobile App\nVersi 1.0.0\n\n© 2025 Masjid An-Nabawi\nPusat Kajian & Dakwah Islam Modern'
    );
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity
      style={[styles.settingItem, { backgroundColor: theme.cardBackground }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <View style={[styles.iconContainer, { backgroundColor: theme.islamicGreen + '20' }]}>
          <Ionicons name={icon as any} size={20} color={theme.islamicGreen} />
        </View>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: theme.text }]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.settingSubtitle, { color: theme.textSecondary }]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      
      <View style={styles.settingRight}>
        {rightComponent}
        {showChevron && onPress && (
          <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Pengaturan</Text>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Account Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.textSecondary }]}>
            AKUN
          </Text>
          
          {isAuthenticated && user ? (
            <>
              {/* User Info */}
              <View style={[styles.userCard, { backgroundColor: theme.cardBackground }]}>
                <View style={[styles.avatarContainer, { backgroundColor: theme.islamicGreen }]}>
                  <Text style={styles.avatarText}>
                    {user.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={[styles.userName, { color: theme.text }]}>
                    {user.name}
                  </Text>
                  <Text style={[styles.userEmail, { color: theme.textSecondary }]}>
                    {user.email}
                  </Text>
                </View>
              </View>
              
              {renderSettingItem(
                'log-out',
                'Keluar',
                'Keluar dari akun Anda',
                handleLogout,
                undefined,
                false
              )}
            </>
          ) : (
            renderSettingItem(
              'log-in',
              'Masuk',
              'Masuk ke akun An-Nabawi Anda',
              handleLogin
            )
          )}
        </View>

        {/* App Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.textSecondary }]}>
            PENGATURAN APLIKASI
          </Text>
          
          {renderSettingItem(
            'moon',
            'Mode Gelap',
            isDarkMode ? 'Aktif' : 'Nonaktif',
            undefined,
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: Colors.border, true: Colors.islamicGreen }}
              thumbColor={isDarkMode ? Colors.white : Colors.white}
            />,
            false
          )}
          
          {renderSettingItem(
            'notifications',
            'Notifikasi',
            'Atur pengingat sholat dan kajian',
            () => Alert.alert('Info', 'Fitur notifikasi akan segera hadir!')
          )}
          
          {renderSettingItem(
            'language',
            'Bahasa',
            'Indonesia',
            () => Alert.alert('Info', 'Fitur ganti bahasa akan segera hadir!')
          )}
        </View>

        {/* App Info */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.textSecondary }]}>
            INFORMASI
          </Text>
          
          {renderSettingItem(
            'information-circle',
            'Tentang Aplikasi',
            'Versi 1.0.0',
            handleAbout
          )}
          
          {renderSettingItem(
            'help-circle',
            'Bantuan',
            'FAQ dan dukungan',
            () => Alert.alert('Info', 'Fitur bantuan akan segera hadir!')
          )}
          
          {renderSettingItem(
            'star',
            'Beri Rating',
            'Bantu kami dengan memberikan rating',
            () => Alert.alert('Info', 'Terima kasih! Fitur rating akan segera hadir!')
          )}
          
          {renderSettingItem(
            'share',
            'Bagikan Aplikasi',
            'Ajak teman menggunakan aplikasi ini',
            () => Alert.alert('Info', 'Fitur berbagi akan segera hadir!')
          )}
        </View>

        {/* Bottom spacing */}
        <View style={{ height: Layout.spacing.xl }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    ...FontStyles.h2,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: Layout.spacing.lg,
  },
  sectionTitle: {
    ...FontStyles.caption,
    fontWeight: '600',
    marginBottom: Layout.spacing.sm,
    marginHorizontal: Layout.containerPadding,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Layout.spacing.md,
    marginHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  avatarText: {
    ...FontStyles.h3,
    color: Colors.white,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...FontStyles.h4,
    marginBottom: Layout.spacing.xs,
  },
  userEmail: {
    ...FontStyles.bodySmall,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    marginHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    ...FontStyles.body,
    marginBottom: 2,
  },
  settingSubtitle: {
    ...FontStyles.caption,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Layout.spacing.sm,
  },
});

export default SettingsScreen;
