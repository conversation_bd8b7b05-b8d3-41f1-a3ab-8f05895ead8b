import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';

const QuranScreen: React.FC = () => {
  const { theme } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Al-Quran</Text>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.comingSoon}>
          <Ionicons name="book" size={80} color={theme.islamicGreen} />
          <Text style={[styles.comingSoonTitle, { color: theme.text }]}>
            Al-Quran Digital
          </Text>
          <Text style={[styles.comingSoonText, { color: theme.textSecondary }]}>
            Fitur Al-Quran digital dengan terjemahan dan audio akan segera hadir
          </Text>
          
          <TouchableOpacity style={[styles.button, { backgroundColor: theme.islamicGreen }]}>
            <Text style={styles.buttonText}>Baca Al-Quran Online</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    ...FontStyles.h2,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  comingSoon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing['2xl'],
  },
  comingSoonTitle: {
    ...FontStyles.h3,
    marginTop: Layout.spacing.lg,
    marginBottom: Layout.spacing.md,
    textAlign: 'center',
  },
  comingSoonText: {
    ...FontStyles.body,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Layout.spacing.xl,
  },
  button: {
    paddingHorizontal: Layout.spacing.xl,
    paddingVertical: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
  },
  buttonText: {
    ...FontStyles.button,
    color: Colors.white,
  },
});

export default QuranScreen;
