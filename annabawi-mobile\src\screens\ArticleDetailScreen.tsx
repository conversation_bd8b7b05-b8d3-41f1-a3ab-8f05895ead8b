import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';

const ArticleDetailScreen: React.FC = () => {
  const { theme } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <ScrollView style={styles.content}>
        <View style={styles.placeholder}>
          <Text style={[styles.title, { color: theme.text }]}>
            Detail Artikel
          </Text>
          <Text style={[styles.subtitle, { color: theme.textSecondary }]}>
            Fitur detail artikel akan segera hadir
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing['2xl'],
  },
  title: {
    ...FontStyles.h2,
    marginBottom: Layout.spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    ...FontStyles.body,
    textAlign: 'center',
  },
});

export default ArticleDetailScreen;
