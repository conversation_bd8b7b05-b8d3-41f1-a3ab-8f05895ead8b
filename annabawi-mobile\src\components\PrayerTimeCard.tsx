import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { PrayerTimes, HijriDate, Location } from '../types';

interface PrayerTimeCardProps {
  prayerTimes?: PrayerTimes;
  hijriDate?: HijriDate;
  location?: Location;
  loading?: boolean;
  onLocationPress?: () => void;
}

const PrayerTimeCard: React.FC<PrayerTimeCardProps> = ({
  prayerTimes,
  hijriDate,
  location,
  loading = false,
  onLocationPress,
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [nextPrayer, setNextPrayer] = useState<string>('');
  const [timeToNext, setTimeToNext] = useState<string>('');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (prayerTimes) {
      calculateNextPrayer();
    }
  }, [prayerTimes, currentTime]);

  const calculateNextPrayer = () => {
    if (!prayerTimes) return;

    const now = currentTime;
    const prayers = [
      { name: 'Subuh', time: prayerTimes.fajr },
      { name: 'Dzuhur', time: prayerTimes.dhuhr },
      { name: 'Ashar', time: prayerTimes.asr },
      { name: 'Maghrib', time: prayerTimes.maghrib },
      { name: 'Isya', time: prayerTimes.isha },
    ];

    const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();

    for (const prayer of prayers) {
      const [hours, minutes] = prayer.time.split(':').map(Number);
      const prayerTimeMinutes = hours * 60 + minutes;

      if (prayerTimeMinutes > currentTimeMinutes) {
        setNextPrayer(prayer.name);
        const diff = prayerTimeMinutes - currentTimeMinutes;
        const diffHours = Math.floor(diff / 60);
        const diffMinutes = diff % 60;
        setTimeToNext(`${diffHours.toString().padStart(2, '0')}:${diffMinutes.toString().padStart(2, '0')}`);
        return;
      }
    }

    // If no prayer found today, next is Subuh tomorrow
    setNextPrayer('Subuh');
    const [hours, minutes] = prayerTimes.fajr.split(':').map(Number);
    const tomorrowSubuh = (24 * 60) + (hours * 60) + minutes;
    const diff = tomorrowSubuh - currentTimeMinutes;
    const diffHours = Math.floor(diff / 60);
    const diffMinutes = diff % 60;
    setTimeToNext(`${diffHours.toString().padStart(2, '0')}:${diffMinutes.toString().padStart(2, '0')}`);
  };

  const formatCurrentTime = () => {
    return currentTime.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const formatDate = () => {
    return currentTime.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={[Colors.islamicGreen, Colors.islamicGreenLight]}
          style={styles.gradient}
        >
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Memuat jadwal sholat...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[Colors.islamicGreen, Colors.islamicGreenLight]}
        style={styles.gradient}
      >
        {/* Header with location */}
        <TouchableOpacity style={styles.header} onPress={onLocationPress}>
          <View style={styles.locationContainer}>
            <Ionicons name="location" size={16} color={Colors.white} />
            <Text style={styles.locationText}>
              {location?.city || 'Tangerang, Tangerang (Ganti)'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Current Time */}
        <View style={styles.timeContainer}>
          <Text style={styles.currentTime}>{formatCurrentTime()}</Text>
          <Text style={styles.nextPrayerLabel}>- {timeToNext}</Text>
        </View>

        {/* Date */}
        <Text style={styles.dateText}>{formatDate()}</Text>
        {hijriDate && (
          <Text style={styles.hijriText}>
            {hijriDate.day} {hijriDate.month.en} {hijriDate.year} / {hijriDate.designation.abbreviated} {hijriDate.year}
          </Text>
        )}

        {/* Next Prayer */}
        {nextPrayer && (
          <View style={styles.nextPrayerContainer}>
            <Text style={styles.nextPrayerText}>{nextPrayer} {timeToNext} WIB</Text>
          </View>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.xl,
    overflow: 'hidden',
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  gradient: {
    padding: Layout.spacing.lg,
    minHeight: Layout.prayerTimeCardHeight,
  },
  header: {
    marginBottom: Layout.spacing.md,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    ...FontStyles.bodySmall,
    color: Colors.white,
    marginLeft: Layout.spacing.xs,
  },
  timeContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  currentTime: {
    ...FontStyles.h1,
    color: Colors.white,
    fontSize: 48,
    fontWeight: 'bold',
  },
  nextPrayerLabel: {
    ...FontStyles.body,
    color: Colors.islamicGold,
    marginTop: -Layout.spacing.sm,
  },
  dateText: {
    ...FontStyles.body,
    color: Colors.white,
    textAlign: 'center',
    marginBottom: Layout.spacing.xs,
  },
  hijriText: {
    ...FontStyles.bodySmall,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: Layout.spacing.md,
  },
  nextPrayerContainer: {
    alignItems: 'center',
  },
  nextPrayerText: {
    ...FontStyles.h4,
    color: Colors.islamicGold,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...FontStyles.body,
    color: Colors.white,
  },
});

export default PrayerTimeCard;
