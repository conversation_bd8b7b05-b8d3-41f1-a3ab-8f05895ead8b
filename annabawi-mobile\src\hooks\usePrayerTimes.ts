import { useState, useEffect } from 'react';
import { PrayerTimes } from '../types';
import api from '../services/api';

interface UsePrayerTimesResult {
  prayerTimes: PrayerTimes | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePrayerTimes = (cityId?: string): UsePrayerTimesResult => {
  const [prayerTimes, setPrayerTimes] = useState<PrayerTimes | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPrayerTimes = async () => {
    if (!cityId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await api.prayerTimes.getByCity(cityId);
      
      if (response.status && response.data && response.data.jadwal) {
        const jadwal = response.data.jadwal;
        const times: PrayerTimes = {
          fajr: jadwal.subuh,
          sunrise: jadwal.terbit,
          dhuhr: jadwal.dzuhur,
          asr: jadwal.ashar,
          maghrib: jadwal.maghrib,
          isha: jadwal.isha,
        };
        setPrayerTimes(times);
      } else {
        throw new Error('Invalid prayer times data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch prayer times');
      console.error('Error fetching prayer times:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await fetchPrayerTimes();
  };

  useEffect(() => {
    fetchPrayerTimes();
  }, [cityId]);

  return {
    prayerTimes,
    loading,
    error,
    refetch,
  };
};
