import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';

// Components
import PrayerTimeCard from '../components/PrayerTimeCard';
import ShortcutGrid from '../components/ShortcutGrid';

// Types
import { PrayerTimes, HijriDate, Location, ShortcutItem, Article, Event } from '../types';

const HomeScreen: React.FC = () => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [prayerTimes, setPrayerTimes] = useState<PrayerTimes | undefined>();
  const [hijriDate, setHijriDate] = useState<HijriDate | undefined>();
  const [location, setLocation] = useState<Location | undefined>();
  const [loading, setLoading] = useState(true);
  const [recentArticles, setRecentArticles] = useState<Article[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadPrayerTimes(),
        loadHijriDate(),
        loadRecentContent(),
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPrayerTimes = async () => {
    try {
      // Mock data - replace with actual API call
      const mockPrayerTimes: PrayerTimes = {
        fajr: '04:43',
        sunrise: '05:56',
        dhuhr: '12:15',
        asr: '15:30',
        maghrib: '18:45',
        isha: '20:00',
      };
      setPrayerTimes(mockPrayerTimes);
      
      const mockLocation: Location = {
        latitude: -6.2088,
        longitude: 106.8456,
        city: 'Tangerang',
        country: 'Indonesia',
      };
      setLocation(mockLocation);
    } catch (error) {
      console.error('Error loading prayer times:', error);
    }
  };

  const loadHijriDate = async () => {
    try {
      // Mock data - replace with actual API call
      const mockHijriDate: HijriDate = {
        date: '15 Safar 1447',
        day: 15,
        month: {
          number: 2,
          en: 'Safar',
          ar: 'صفر',
        },
        year: 1447,
        designation: {
          abbreviated: 'AH',
          expanded: 'Anno Hegirae',
        },
      };
      setHijriDate(mockHijriDate);
    } catch (error) {
      console.error('Error loading Hijri date:', error);
    }
  };

  const loadRecentContent = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockArticles: Article[] = [
        {
          id: 1,
          title: 'Keutamaan Membaca Al-Quran di Bulan Ramadan',
          slug: 'keutamaan-membaca-al-quran-ramadan',
          content: '',
          featured_image_url: '',
          author_name: 'Ustadz Ahmad',
          created_at: '2025-01-15',
          tags: ['ramadan', 'al-quran'],
        },
      ];
      setRecentArticles(mockArticles);

      const mockEvents: Event[] = [
        {
          id: 1,
          title: 'Kajian Tafsir Al-Quran',
          speaker: 'Ustadz Muhammad',
          start_time: '2025-01-20 19:30:00',
          end_time: '2025-01-20 21:00:00',
          location: 'Masjid An-Nabawi',
          description: 'Kajian rutin tafsir Al-Quran setiap Minggu malam',
          tags: ['kajian', 'tafsir'],
        },
      ];
      setUpcomingEvents(mockEvents);
    } catch (error) {
      console.error('Error loading recent content:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleShortcutPress = (shortcut: ShortcutItem) => {
    if (shortcut.screen) {
      // Navigate to screen
      console.log('Navigate to:', shortcut.screen);
    } else if (shortcut.action) {
      shortcut.action();
    } else {
      Alert.alert('Info', `Fitur ${shortcut.title} akan segera hadir!`);
    }
  };

  const handleLocationPress = () => {
    Alert.alert('Lokasi', 'Fitur pemilihan lokasi akan segera hadir!');
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      Alert.alert('Pencarian', `Mencari: ${searchQuery}`);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={[styles.greeting, { color: theme.text }]}>
              Assalamu'alaikum
            </Text>
            <Text style={[styles.appName, { color: theme.islamicGreen }]}>
              NU Online
            </Text>
          </View>
        </View>

        {/* Prayer Time Card */}
        <PrayerTimeCard
          prayerTimes={prayerTimes}
          hijriDate={hijriDate}
          location={location}
          loading={loading}
          onLocationPress={handleLocationPress}
        />

        {/* Shortcut Grid */}
        <ShortcutGrid onShortcutPress={handleShortcutPress} />

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: theme.backgroundSecondary }]}>
            <Ionicons name="search" size={20} color={theme.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.text }]}
              placeholder="Cari doa, wirid, artikel..."
              placeholderTextColor={theme.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
          </View>
        </View>

        {/* Recent Content Section */}
        <View style={styles.contentSection}>
          <TouchableOpacity style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Headline
            </Text>
            <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
          </TouchableOpacity>
          
          {/* Content cards would go here */}
          <View style={[styles.contentCard, { backgroundColor: theme.cardBackground }]}>
            <Text style={[styles.contentTitle, { color: theme.text }]}>
              Dukung NU Online Super App
            </Text>
            <Text style={[styles.contentSubtitle, { color: theme.textSecondary }]}>
              Infaq 10.000/bulan
            </Text>
          </View>
        </View>

        {/* Bottom spacing */}
        <View style={{ height: Layout.spacing.xl }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Layout.containerPadding,
    paddingTop: Layout.spacing.md,
    paddingBottom: Layout.spacing.lg,
  },
  headerContent: {
    alignItems: 'center',
  },
  greeting: {
    ...FontStyles.body,
    marginBottom: Layout.spacing.xs,
  },
  appName: {
    ...FontStyles.h2,
    fontWeight: 'bold',
  },
  searchContainer: {
    paddingHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.lg,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: Layout.spacing.sm,
    ...FontStyles.body,
  },
  contentSection: {
    paddingHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  sectionTitle: {
    ...FontStyles.h3,
  },
  contentCard: {
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contentTitle: {
    ...FontStyles.h4,
    marginBottom: Layout.spacing.xs,
  },
  contentSubtitle: {
    ...FontStyles.bodySmall,
  },
});

export default HomeScreen;
