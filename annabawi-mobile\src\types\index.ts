// Types untuk aplikasi mobile An-Nabawi

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  roles: string[];
}

export interface Article {
  id: number;
  title: string;
  slug: string;
  content: string;
  featured_image_url: string;
  author_name: string;
  created_at: string;
  tags: string[];
}

export interface Event {
  id: number;
  title: string;
  speaker: string;
  start_time: string;
  end_time: string;
  location: string;
  description: string;
  tags: string[];
}

export interface PrayerTime {
  name: string;
  time: string;
  isNext?: boolean;
}

export interface PrayerTimes {
  fajr: string;
  sunrise: string;
  dhuhr: string;
  asr: string;
  maghrib: string;
  isha: string;
}

export interface HijriDate {
  date: string;
  day: number;
  month: {
    number: number;
    en: string;
    ar: string;
  };
  year: number;
  designation: {
    abbreviated: string;
    expanded: string;
  };
}

export interface Location {
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
}

export interface ShortcutItem {
  id: string;
  title: string;
  icon: string;
  color: string;
  screen?: string;
  action?: () => void;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: boolean;
}
