import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const Layout = {
  // Screen dimensions
  window: {
    width,
    height,
  },
  
  // Breakpoints
  isSmallDevice: width < 375,
  isTablet: width >= 768,
  
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },
  
  // Border radius
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 24,
    full: 9999,
  },
  
  // Header heights
  headerHeight: 60,
  tabBarHeight: 80,
  statusBarHeight: 44, // iOS default
  
  // Container padding
  containerPadding: 16,
  
  // Card dimensions
  cardMinHeight: 120,
  shortcutSize: 80,
  
  // Prayer time card
  prayerTimeCardHeight: 200,
  
  // Bottom sheet
  bottomSheetMinHeight: 200,
  bottomSheetMaxHeight: height * 0.8,
  
  // Modal
  modalPadding: 20,
  
  // Safe area
  safeAreaTop: 44,
  safeAreaBottom: 34,
};

// Helper functions
export const getResponsiveSize = (size: number) => {
  if (Layout.isSmallDevice) {
    return size * 0.9;
  }
  if (Layout.isTablet) {
    return size * 1.2;
  }
  return size;
};

export const getGridColumns = () => {
  if (Layout.isTablet) {
    return 4;
  }
  return 2;
};

export const getShortcutColumns = () => {
  if (Layout.isTablet) {
    return 6;
  }
  return 4;
};
