// API service layer untuk integrasi dengan backend
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Article, Event, User, ApiResponse } from '../types';

// Base URL - ganti dengan URL backend yang sebenarnya
const BASE_URL = 'http://localhost:3001/api';

// Helper function untuk membuat request dengan auth header
const createAuthHeaders = async () => {
  const token = await AsyncStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Generic API request function
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = `${BASE_URL}${endpoint}`;
  const headers = await createAuthHeaders();
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<{ token: string }> => {
    return apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  },

  register: async (
    name: string,
    email: string,
    password: string,
    phone?: string
  ): Promise<{ message: string }> => {
    return apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ name, email, password, phone }),
    });
  },
};

// Articles API
export const articlesAPI = {
  getAll: async (): Promise<Article[]> => {
    return apiRequest('/articles');
  },

  getById: async (id: number): Promise<Article> => {
    return apiRequest(`/articles/${id}`);
  },

  getBySlug: async (slug: string): Promise<Article> => {
    return apiRequest(`/articles/slug/${slug}`);
  },
};

// Events API
export const eventsAPI = {
  getAll: async (): Promise<Event[]> => {
    return apiRequest('/events');
  },

  getById: async (id: number): Promise<Event> => {
    return apiRequest(`/events/${id}`);
  },

  getUpcoming: async (): Promise<Event[]> => {
    // Assuming backend has an endpoint for upcoming events
    return apiRequest('/events?upcoming=true');
  },
};

// Prayer Times API (external)
export const prayerTimesAPI = {
  getByCity: async (cityId: string): Promise<any> => {
    const response = await fetch(
      `https://api.myquran.com/v2/sholat/jadwal/${cityId}/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${new Date().getDate()}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch prayer times');
    }
    
    return response.json();
  },

  getCities: async (): Promise<any> => {
    const response = await fetch('https://api.myquran.com/v2/sholat/kota/semua');
    
    if (!response.ok) {
      throw new Error('Failed to fetch cities');
    }
    
    return response.json();
  },
};

// Hijri Date API (external)
export const hijriDateAPI = {
  getToday: async (): Promise<any> => {
    const today = new Date();
    const response = await fetch(
      `https://api.aladhan.com/v1/gToH/${today.getDate()}-${today.getMonth() + 1}-${today.getFullYear()}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch Hijri date');
    }
    
    return response.json();
  },
};

// Location API (external)
export const locationAPI = {
  reverseGeocode: async (latitude: number, longitude: number): Promise<any> => {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${latitude}&lon=${longitude}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to reverse geocode');
    }
    
    return response.json();
  },
};

// Export all APIs
export default {
  auth: authAPI,
  articles: articlesAPI,
  events: eventsAPI,
  prayerTimes: prayerTimesAPI,
  hijriDate: hijriDateAPI,
  location: locationAPI,
};
