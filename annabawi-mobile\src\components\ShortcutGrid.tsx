import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout, getShortcutColumns } from '../constants/Layout';
import { ShortcutItem } from '../types';

const { width } = Dimensions.get('window');

interface ShortcutGridProps {
  onShortcutPress: (shortcut: ShortcutItem) => void;
}

const shortcuts: ShortcutItem[] = [
  {
    id: 'quran',
    title: 'Al-Quran',
    icon: 'book',
    color: Colors.shortcutQuran,
    screen: 'Quran',
  },
  {
    id: 'wirid',
    title: 'Wirid & Doa',
    icon: 'hand-left',
    color: Colors.shortcutWirid,
  },
  {
    id: 'sholat',
    title: 'J<PERSON>wal Sholat',
    icon: 'time',
    color: Colors.shortcutSholat,
  },
  {
    id: 'qiblat',
    title: 'Kiblat',
    icon: 'compass',
    color: Colors.shortcutQiblat,
  },
  {
    id: 'tahlil',
    title: '<PERSON><PERSON><PERSON> & <PERSON><PERSON>',
    icon: 'people',
    color: Colors.shortcutTahlil,
  },
  {
    id: 'maulid',
    title: 'Maulid',
    icon: 'library',
    color: Colors.shortcutMaulid,
  },
  {
    id: 'zakat',
    title: 'Zakat & Sedekah',
    icon: 'heart',
    color: Colors.shortcutZakat,
  },
  {
    id: 'lainnya',
    title: 'Lainnya',
    icon: 'apps',
    color: Colors.shortcutOther,
  },
];

const ShortcutGrid: React.FC<ShortcutGridProps> = ({ onShortcutPress }) => {
  const columns = getShortcutColumns();
  const itemWidth = (width - Layout.containerPadding * 2 - Layout.spacing.md * (columns - 1)) / columns;

  const renderShortcut = (shortcut: ShortcutItem) => (
    <TouchableOpacity
      key={shortcut.id}
      style={[styles.shortcutItem, { width: itemWidth }]}
      onPress={() => onShortcutPress(shortcut)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: shortcut.color }]}>
        <Ionicons
          name={shortcut.icon as any}
          size={28}
          color={Colors.white}
        />
      </View>
      <Text style={styles.shortcutTitle} numberOfLines={2}>
        {shortcut.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.grid}>
        {shortcuts.map(renderShortcut)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Layout.containerPadding,
    marginBottom: Layout.spacing.lg,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: Layout.spacing.md,
  },
  shortcutItem: {
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  iconContainer: {
    width: Layout.shortcutSize,
    height: Layout.shortcutSize,
    borderRadius: Layout.borderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  shortcutTitle: {
    ...FontStyles.caption,
    color: Colors.text,
    textAlign: 'center',
    lineHeight: 14,
    maxWidth: Layout.shortcutSize,
  },
});

export default ShortcutGrid;
