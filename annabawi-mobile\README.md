# An-Nabawi Mobile App

Mobile application untuk Masjid An-Nabawi yang dibangun dengan React Native dan Expo. Aplikasi ini menyediakan fitur-fitur islami seperti jadwal sholat, Al-Quran digital, artikel kajian, dan kalender acara.

## 🚀 Fitur Utama

### 🏠 Home Screen
- **Jadwal Sholat Real-time**: Menampilkan waktu sholat berdasarkan lokasi pengguna
- **Tanggal Hijriah**: Konversi tanggal Ma<PERSON>hi ke Hijriah
- **Shortcut Menu**: Aks<PERSON> cepat ke fitur-fitur utama
- **Search Bar**: Pencarian doa, wirid, dan artikel
- **Headline**: Berita dan artikel terbaru

### 📖 Al-Quran Digital
- Baca Al-Quran dengan terjemahan
- Audio recitation (akan datang)
- Bookmark ayat favorit (akan datang)
- Pencarian ayat (akan datang)

### 📰 Artikel & Kajian
- Artikel kajian Islam terbaru
- Kalender acara dan kajian
- Detail artikel dengan gambar
- Kategori dan tag artikel

### ⚙️ Pengaturan
- **Mode Gelap/Terang**: Toggle dark mode
- **Manajemen Akun**: Login, register, logout
- **Info Aplikasi**: Versi dan informasi developer
- **Notifikasi**: Pengaturan reminder sholat (akan datang)

## 🛠️ Teknologi

- **React Native**: Framework mobile development
- **Expo**: Development platform dan tools
- **TypeScript**: Type-safe JavaScript
- **React Navigation**: Navigation library
- **Expo Vector Icons**: Icon library
- **React Native Paper**: UI components
- **AsyncStorage**: Local data storage
- **Linear Gradient**: Gradient backgrounds

## 📱 Arsitektur

```
src/
├── components/          # Reusable UI components
│   ├── PrayerTimeCard.tsx
│   ├── ShortcutGrid.tsx
│   └── ...
├── screens/            # Screen components
│   ├── HomeScreen.tsx
│   ├── QuranScreen.tsx
│   ├── ArticlesScreen.tsx
│   ├── CalendarScreen.tsx
│   ├── SettingsScreen.tsx
│   └── ...
├── navigation/         # Navigation configuration
│   ├── AppNavigator.tsx
│   └── TabNavigator.tsx
├── context/           # React Context providers
│   ├── AuthContext.tsx
│   └── ThemeContext.tsx
├── services/          # API services
│   └── api.ts
├── hooks/             # Custom React hooks
│   ├── usePrayerTimes.ts
│   ├── useHijriDate.ts
│   └── useLocation.ts
├── constants/         # App constants
│   ├── Colors.ts
│   ├── Fonts.ts
│   └── Layout.ts
└── types/            # TypeScript type definitions
    └── index.ts
```

## 🎨 Design System

### Warna
- **Islamic Green**: `#008037` - Warna utama aplikasi
- **Islamic Gold**: `#FFD700` - Warna aksen dan highlight
- **Background**: `#FFFFFF` (light) / `#1A1A1A` (dark)
- **Text**: `#2D5016` (light) / `#FFFFFF` (dark)

### Font
- **Primary**: Inter - Font utama untuk UI
- **Secondary**: Montserrat - Font alternatif
- **Arabic**: Amiri - Font untuk teks Arab

### Spacing
- **xs**: 4px
- **sm**: 8px
- **md**: 16px
- **lg**: 24px
- **xl**: 32px
- **2xl**: 48px

## 🔧 Setup Development

### Prerequisites
- Node.js (v16 atau lebih baru)
- npm atau yarn
- Expo CLI
- Android Studio (untuk Android development)
- Xcode (untuk iOS development - hanya di macOS)

### Installation

1. Clone repository
```bash
git clone <repository-url>
cd annabawi-mobile
```

2. Install dependencies
```bash
npm install
```

3. Start development server
```bash
npm start
```

4. Run on device/emulator
```bash
# Android
npm run android

# iOS (macOS only)
npm run ios

# Web
npm run web
```

### Environment Setup

Buat file `.env` di root project:
```env
API_BASE_URL=http://localhost:3001/api
```

## 📡 API Integration

Aplikasi terintegrasi dengan backend An-Nabawi Portal:

### Endpoints
- **Auth**: `/api/auth/login`, `/api/auth/register`
- **Articles**: `/api/articles`
- **Events**: `/api/events`

### External APIs
- **Prayer Times**: MyQuran API
- **Hijri Date**: Aladhan API
- **Location**: OpenStreetMap Nominatim

## 🔐 Authentication

Sistem autentikasi menggunakan JWT token:
- Token disimpan di AsyncStorage
- Auto-refresh token (akan datang)
- Logout otomatis saat token expired

## 🌙 Dark Mode

Aplikasi mendukung dark mode dengan:
- Toggle manual di Settings
- Mengikuti system preference
- Persistent preference storage

## 📱 Platform Support

- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 11.0+
- **Web**: Modern browsers (development only)

## 🚀 Build & Deployment

### Development Build
```bash
expo build:android
expo build:ios
```

### Production Build
```bash
# Android APK
expo build:android -t apk

# Android App Bundle
expo build:android -t app-bundle

# iOS
expo build:ios
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 📝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

Copyright © 2025 Masjid An-Nabawi. All rights reserved.

## 📞 Support

- **Email**: <EMAIL>
- **WhatsApp**: [Join our channel](https://www.whatsapp.com/channel/0029VaTTWVAGufIryonAMo1z)
- **Website**: [An-Nabawi Portal](https://annabawi.org)

---

**Built with ❤️ for the Muslim Ummah**

*"Dan barangsiapa membangun masjid karena Allah, maka Allah akan membangunkan baginya rumah di surga."* - Hadits
