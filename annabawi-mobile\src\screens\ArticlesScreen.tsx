import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';
import { useTheme } from '../context/ThemeContext';
import { Article } from '../types';

const ArticlesScreen: React.FC = () => {
  const { theme } = useTheme();
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadArticles();
  }, []);

  const loadArticles = async () => {
    try {
      // Mock data - replace with actual API call
      const mockArticles: Article[] = [
        {
          id: 1,
          title: 'Keutamaan Membaca Al-Quran di Bulan <PERSON>',
          slug: 'keutamaan-membaca-al-quran-ramadan',
          content: 'Lorem ipsum dolor sit amet...',
          featured_image_url: '',
          author_name: 'Ustadz Ahmad',
          created_at: '2025-01-15',
          tags: ['ramadan', 'al-quran'],
        },
        {
          id: 2,
          title: 'Adab Berinteraksi dalam Islam',
          slug: 'adab-berinteraksi-dalam-islam',
          content: 'Lorem ipsum dolor sit amet...',
          featured_image_url: '',
          author_name: 'Ustadz Muhammad',
          created_at: '2025-01-14',
          tags: ['akhlak', 'adab'],
        },
      ];
      setArticles(mockArticles);
    } catch (error) {
      console.error('Error loading articles:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadArticles();
    setRefreshing(false);
  };

  const renderArticleCard = (article: Article) => (
    <TouchableOpacity
      key={article.id}
      style={[styles.articleCard, { backgroundColor: theme.cardBackground }]}
    >
      <View style={styles.articleContent}>
        <Text style={[styles.articleTitle, { color: theme.text }]} numberOfLines={2}>
          {article.title}
        </Text>
        <Text style={[styles.articleMeta, { color: theme.textSecondary }]}>
          {article.author_name} • {new Date(article.created_at).toLocaleDateString('id-ID')}
        </Text>
        <View style={styles.tagsContainer}>
          {article.tags.slice(0, 2).map((tag, index) => (
            <View key={index} style={[styles.tag, { backgroundColor: theme.islamicGreen + '20' }]}>
              <Text style={[styles.tagText, { color: theme.islamicGreen }]}>
                {tag}
              </Text>
            </View>
          ))}
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Artikel</Text>
      </View>
      
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: theme.textSecondary }]}>
              Memuat artikel...
            </Text>
          </View>
        ) : (
          <View style={styles.articlesContainer}>
            {articles.map(renderArticleCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    ...FontStyles.h2,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Layout.spacing['2xl'],
  },
  loadingText: {
    ...FontStyles.body,
  },
  articlesContainer: {
    paddingHorizontal: Layout.containerPadding,
    paddingVertical: Layout.spacing.lg,
  },
  articleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  articleContent: {
    flex: 1,
  },
  articleTitle: {
    ...FontStyles.h4,
    marginBottom: Layout.spacing.xs,
    lineHeight: 22,
  },
  articleMeta: {
    ...FontStyles.caption,
    marginBottom: Layout.spacing.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: Layout.spacing.xs,
  },
  tag: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
  },
  tagText: {
    ...FontStyles.caption,
    fontSize: 10,
  },
});

export default ArticlesScreen;
