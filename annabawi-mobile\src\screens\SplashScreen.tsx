import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { Colors } from '../constants/Colors';
import { FontStyles } from '../constants/Fonts';
import { Layout } from '../constants/Layout';

const { width, height } = Dimensions.get('window');

const SplashScreen: React.FC = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        delay: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <LinearGradient
      colors={[Colors.islamicGreen, Colors.islamicGreenLight]}
      style={styles.container}
    >
      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        <Animatable.View
          animation="pulse"
          iterationCount="infinite"
          duration={3000}
          style={styles.patternCircle1}
        />
        <Animatable.View
          animation="pulse"
          iterationCount="infinite"
          duration={4000}
          delay={1000}
          style={styles.patternCircle2}
        />
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Logo */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.logoBackground}>
            <Image
              source={require('../../assets/icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
        </Animated.View>

        {/* App Name */}
        <Animated.View
          style={[
            styles.textContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.appName}>An-Nabawi</Text>
          <Text style={styles.subtitle}>Islamic Center</Text>
          <Text style={styles.tagline}>Pusat Kajian & Dakwah Islam Modern</Text>
        </Animated.View>

        {/* Loading Indicator */}
        <Animated.View
          style={[
            styles.loadingContainer,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <Animatable.View
            animation="rotate"
            iterationCount="infinite"
            duration={2000}
            style={styles.loadingSpinner}
          >
            <View style={styles.spinnerDot} />
            <View style={[styles.spinnerDot, { transform: [{ rotate: '45deg' }] }]} />
            <View style={[styles.spinnerDot, { transform: [{ rotate: '90deg' }] }]} />
            <View style={[styles.spinnerDot, { transform: [{ rotate: '135deg' }] }]} />
          </Animatable.View>
          <Text style={styles.loadingText}>Memuat...</Text>
        </Animated.View>
      </View>

      {/* Bottom Text */}
      <Animated.View
        style={[
          styles.bottomContainer,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <Text style={styles.versionText}>Versi 1.0.0</Text>
        <Text style={styles.copyrightText}>© 2025 Masjid An-Nabawi</Text>
      </Animated.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: width,
    height: height,
  },
  patternCircle1: {
    position: 'absolute',
    width: width * 1.5,
    height: width * 1.5,
    borderRadius: width * 0.75,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    top: -width * 0.5,
    left: -width * 0.25,
  },
  patternCircle2: {
    position: 'absolute',
    width: width * 1.2,
    height: width * 1.2,
    borderRadius: width * 0.6,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    bottom: -width * 0.4,
    right: -width * 0.2,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.xl,
  },
  logoContainer: {
    marginBottom: Layout.spacing.xl,
  },
  logoBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  logo: {
    width: 80,
    height: 80,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: Layout.spacing['2xl'],
  },
  appName: {
    ...FontStyles.h1,
    color: Colors.white,
    marginBottom: Layout.spacing.xs,
    textAlign: 'center',
  },
  subtitle: {
    ...FontStyles.h4,
    color: Colors.islamicGold,
    marginBottom: Layout.spacing.sm,
    textAlign: 'center',
  },
  tagline: {
    ...FontStyles.body,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: Layout.spacing.xl,
  },
  loadingSpinner: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  spinnerDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.islamicGold,
    top: 0,
  },
  loadingText: {
    ...FontStyles.bodySmall,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: Layout.spacing.xl,
    alignItems: 'center',
  },
  versionText: {
    ...FontStyles.caption,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: Layout.spacing.xs,
  },
  copyrightText: {
    ...FontStyles.caption,
    color: 'rgba(255, 255, 255, 0.6)',
  },
});

export default SplashScreen;
