import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { Location as LocationType } from '../types';
import api from '../services/api';

interface UseLocationResult {
  location: LocationType | null;
  loading: boolean;
  error: string | null;
  requestPermission: () => Promise<void>;
  refetch: () => Promise<void>;
}

export const useLocation = (): UseLocationResult => {
  const [location, setLocation] = useState<LocationType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const requestPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission denied');
      }
      await getCurrentLocation();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get location permission');
    }
  };

  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      setError(null);

      const locationResult = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = locationResult.coords;

      // Reverse geocode to get city name
      const geoData = await api.location.reverseGeocode(latitude, longitude);
      
      const locationData: LocationType = {
        latitude,
        longitude,
        city: extractCityName(geoData.display_name),
        country: geoData.address?.country || 'Indonesia',
      };

      setLocation(locationData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get current location');
      console.error('Error getting location:', err);
    } finally {
      setLoading(false);
    }
  };

  const extractCityName = (displayName: string): string => {
    // Extract city name from display name
    const parts = displayName.split(', ');
    // Usually the city is one of the first parts
    for (const part of parts) {
      if (part.includes('Kota') || part.includes('Kabupaten')) {
        return part.replace(/^(Kota |Kabupaten )/, '');
      }
    }
    return parts[0] || 'Unknown';
  };

  const refetch = async () => {
    await getCurrentLocation();
  };

  useEffect(() => {
    requestPermission();
  }, []);

  return {
    location,
    loading,
    error,
    requestPermission,
    refetch,
  };
};
