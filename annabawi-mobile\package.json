{"name": "annabawi-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.4", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.5", "expo": "~53.0.20", "expo-font": "^13.3.2", "expo-linear-gradient": "^14.1.5", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-elements": "^3.4.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-native-super-grid": "^6.0.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}